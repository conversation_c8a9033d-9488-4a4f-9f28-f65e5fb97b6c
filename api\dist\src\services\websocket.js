"use strict";
/**
 * WebSocket Service para notificaciones en tiempo real - Con Autenticación
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketService = void 0;
const ws_1 = require("ws");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
class WebSocketService {
    constructor() {
        this.wss = null;
        this.userConnections = new Map();
        this.tokenToUserMap = new Map();
    }
    /**
     * Inicializar el servidor WebSocket
     */
    initialize(server) {
        this.wss = new ws_1.WebSocketServer({
            server,
            path: "/ws/notifications",
            verifyClient: this.verifyClient.bind(this),
            // Configuración adicional para evitar conflictos con admin panel
            clientTracking: true,
            perMessageDeflate: false,
        });
        this.wss.on("connection", (ws, req) => this.handleConnection(ws, req));
        // Agregar manejo de errores del servidor WebSocket
        this.wss.on("error", (error) => {
            console.error("❌ Error en servidor WebSocket:", error);
        });
    }
    /**
     * Verificar cliente antes de la conexión
     */
    async verifyClient(info) {
        var _a, _b, _c, _d;
        try {
            const url = new URL(info.req.url, `http://${info.req.headers.host}`);
            // Verificar que la conexión es específicamente para WebSocket notifications
            if (!url.pathname.includes("/ws/notifications")) {
                console.log("❌ WebSocket: Ruta no válida para WebSocket");
                return false;
            }
            const token = url.searchParams.get("token") ||
                ((_a = info.req.headers.authorization) === null || _a === void 0 ? void 0 : _a.replace("Bearer ", ""));
            if (!token) {
                console.log("❌ WebSocket: Token no proporcionado, rechazando conexión");
                return false;
            }
            // Intentar verificar el token como token de usuario normal primero
            let decoded = null;
            let isAdminToken = false;
            try {
                // Primero intentar con JWT_SECRET (tokens de usuarios normales)
                decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
                console.log("🔍 WebSocket: Token verificado como token de usuario normal");
            }
            catch (userTokenError) {
                try {
                    // Si falla, intentar con ADMIN_JWT_SECRET (tokens de admin)
                    decoded = jsonwebtoken_1.default.verify(token, process.env.ADMIN_JWT_SECRET);
                    isAdminToken = true;
                    console.log("🔍 WebSocket: Token verificado como token de administrador");
                }
                catch (adminTokenError) {
                    console.log("❌ WebSocket: Token inválido para ambos tipos de JWT");
                    return false;
                }
            }
            if (!decoded || (!decoded.id && !decoded.userId)) {
                console.log("❌ WebSocket: Token no contiene ID de usuario válido");
                return false;
            }
            // Obtener el ID del usuario según el tipo de token
            const userId = decoded.id || decoded.userId;
            let user = null;
            if (isAdminToken) {
                // Para tokens de admin, buscar en la tabla de admin users
                try {
                    user = await strapi.db.query("admin::user").findOne({
                        where: { id: userId },
                        populate: ["roles"],
                    });
                    if (user) {
                        // Adaptar la estructura para compatibilidad
                        user.role = ((_b = user.roles) === null || _b === void 0 ? void 0 : _b[0]) || { name: "admin", type: "admin" };
                        console.log(`🔍 WebSocket: Usuario admin encontrado: ${user.email || user.username}`);
                    }
                }
                catch (error) {
                    console.log("❌ WebSocket: Error buscando usuario admin:", error);
                    return false;
                }
            }
            else {
                // Para tokens normales, buscar en users-permissions
                try {
                    user = await strapi.entityService.findOne("plugin::users-permissions.user", userId, { populate: ["role"] });
                    console.log(`🔍 WebSocket: Usuario normal encontrado: ${(user === null || user === void 0 ? void 0 : user.email) || (user === null || user === void 0 ? void 0 : user.username)}`);
                }
                catch (error) {
                    console.log("❌ WebSocket: Error buscando usuario normal:", error);
                    return false;
                }
            }
            if (!user) {
                console.log(`❌ WebSocket: Usuario ${userId} no encontrado`);
                return false;
            }
            // Generar un ID único para esta conexión
            const connectionKey = `conn_${userId}_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 11)}`;
            // Guardar datos en la request Y en almacenamiento temporal
            info.req.userId = userId;
            info.req.user = user;
            info.req.connectionKey = connectionKey;
            info.req.isAdminToken = isAdminToken;
            // Almacenar por token para recuperar en handleConnection
            this.tokenToUserMap.set(token, {
                userId: userId,
                user: user,
                isAdminToken: isAdminToken,
            });
            console.log(`✅ WebSocket: Cliente verificado para ${isAdminToken ? "admin" : "usuario"} ${user.username || user.email || "Usuario"} (ID: ${userId}) - Rol: ${((_c = user.role) === null || _c === void 0 ? void 0 : _c.name) || ((_d = user.role) === null || _d === void 0 ? void 0 : _d.type) || "sin rol"} - Key: ${connectionKey}`);
            return true;
        }
        catch (error) {
            console.error("❌ WebSocket: Error verificando cliente:", error);
            return false;
        }
    }
    /**
     * Manejar nueva conexión WebSocket
     */
    handleConnection(ws, req) {
        // Extraer token de la URL
        const url = new URL(req.url, `http://${req.headers.host}`);
        const token = url.searchParams.get("token");
        if (!token) {
            console.error("❌ WebSocket: Token no encontrado en la URL");
            ws.close(1008, "Token requerido");
            return;
        }
        // Buscar datos del usuario por token
        const userData = this.tokenToUserMap.get(token);
        if (!userData) {
            console.error("❌ WebSocket: Datos de usuario no encontrados para el token");
            ws.close(1008, "Usuario no autenticado");
            return;
        }
        const { userId, user, isAdminToken } = userData;
        // Limpiar datos temporales
        this.tokenToUserMap.delete(token);
        ws.userId = userId;
        ws.user = user;
        ws.isAuthenticated = true;
        const userName = user.username || user.email || `Usuario ${userId}`;
        const userType = isAdminToken ? "Admin" : "Usuario";
        console.log(`🔗 Nueva conexión WebSocket AUTENTICADA: ${userType} ${userName} (ID: ${userId})`);
        // Agregar conexión al mapa de usuarios
        this.addUserConnection(ws.userId, ws, ws.user);
        // Enviar mensaje de bienvenida
        this.sendToClient(ws, {
            type: "connection",
            message: "Conectado al servidor de notificaciones",
            user: {
                id: ws.user.id || ws.userId,
                username: userName,
                isAdmin: isAdminToken || false,
            },
            timestamp: new Date().toISOString(),
        });
        // Manejar mensajes del cliente
        ws.on("message", (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleClientMessage(ws, message);
            }
            catch (error) {
                console.error("❌ Error procesando mensaje del cliente:", error);
            }
        });
        // Manejar desconexión
        ws.on("close", () => {
            const userName = ws.user.username || ws.user.email || `Usuario ${ws.userId}`;
            console.log(`🔌 Desconexión WebSocket: ${userName} (ID: ${ws.userId})`);
            this.removeUserConnection(ws.userId, ws);
        });
        // Manejar errores
        ws.on("error", (error) => {
            console.error(`❌ Error WebSocket para usuario ${ws.userId}:`, error);
            this.removeUserConnection(ws.userId, ws);
        });
    }
    /**
     * Manejar mensajes del cliente
     */
    handleClientMessage(ws, message) {
        console.log(`📨 Mensaje recibido de usuario ${ws.userId}:`, message);
        switch (message.type) {
            case "ping":
                this.sendToClient(ws, {
                    type: "pong",
                    timestamp: new Date().toISOString(),
                });
                break;
            case "subscribe":
                // El cliente puede suscribirse a tipos específicos de notificaciones
                console.log(`📡 Usuario ${ws.userId} se suscribió a: ${message.topics}`);
                break;
            default:
                console.log(`❓ Tipo de mensaje desconocido: ${message.type}`);
        }
    }
    /**
     * Agregar conexión de usuario
     */
    addUserConnection(userId, ws, user) {
        if (!this.userConnections.has(userId)) {
            this.userConnections.set(userId, []);
        }
        const connections = this.userConnections.get(userId);
        connections.push({
            ws,
            userId,
            user,
            connectedAt: new Date(),
        });
        console.log(`📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`);
    }
    /**
     * Remover conexión de usuario
     */
    removeUserConnection(userId, ws) {
        const connections = this.userConnections.get(userId);
        if (!connections)
            return;
        const index = connections.findIndex((conn) => conn.ws === ws);
        if (index !== -1) {
            connections.splice(index, 1);
        }
        if (connections.length === 0) {
            this.userConnections.delete(userId);
        }
        console.log(`📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`);
    }
    /**
     * Enviar mensaje a un cliente específico
     */
    sendToClient(ws, data) {
        if (ws.readyState === ws_1.WebSocket.OPEN) {
            try {
                ws.send(JSON.stringify(data));
                return true;
            }
            catch (error) {
                console.error("❌ Error enviando mensaje:", error);
                return false;
            }
        }
        return false;
    }
    /**
     * Broadcast a todas las conexiones autenticadas
     */
    broadcast(data) {
        let sentCount = 0;
        this.userConnections.forEach((connections) => {
            connections.forEach((connection) => {
                if (this.sendToClient(connection.ws, data)) {
                    sentCount++;
                }
            });
        });
        console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
        return sentCount;
    }
    /**
     * Enviar notificación a un usuario específico
     */
    sendNotificationToUser(userId, notification) {
        // Verificar si userId es NaN
        if (isNaN(userId)) {
            console.log(`❌ ERROR: userId es NaN. Valor original: ${userId}`);
            return false;
        }
        const connections = this.userConnections.get(userId);
        if (!connections || connections.length === 0) {
            console.log(`❌ Usuario ${userId} no tiene conexiones activas`);
            return false;
        }
        let sentCount = 0;
        connections.forEach((connection) => {
            if (this.sendToClient(connection.ws, {
                type: "notification",
                data: notification,
                timestamp: new Date().toISOString(),
            })) {
                sentCount++;
            }
        });
        console.log(`📤 Notificación enviada a ${sentCount} conexión(es) del usuario ${userId}`);
        return sentCount > 0;
    }
    /**
     * Enviar notificación a múltiples usuarios
     */
    sendNotificationToUsers(userIds, notification) {
        let totalSent = 0;
        userIds.forEach((userId) => {
            if (this.sendNotificationToUser(userId, notification)) {
                totalSent++;
            }
        });
        return totalSent;
    }
    /**
     * Enviar notificación por rol
     */
    sendNotificationToRole(roleName, notification) {
        let sentCount = 0;
        this.userConnections.forEach((connections, userId) => {
            var _a, _b;
            const user = (_a = connections[0]) === null || _a === void 0 ? void 0 : _a.user;
            if (((_b = user === null || user === void 0 ? void 0 : user.role) === null || _b === void 0 ? void 0 : _b.name) === roleName) {
                if (this.sendNotificationToUser(userId, notification)) {
                    sentCount++;
                }
            }
        });
        console.log(`📤 Notificación enviada a ${sentCount} usuario(s) con rol ${roleName}`);
        return sentCount;
    }
    /**
     * Obtener estadísticas detalladas
     */
    getStats() {
        const totalConnections = Array.from(this.userConnections.values()).reduce((total, connections) => total + connections.length, 0);
        const connectedUsers = this.userConnections.size;
        const userStats = Array.from(this.userConnections.entries()).map(([userId, connections]) => {
            var _a, _b, _c, _d, _e;
            return ({
                userId,
                username: ((_b = (_a = connections[0]) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.username) || ((_d = (_c = connections[0]) === null || _c === void 0 ? void 0 : _c.user) === null || _d === void 0 ? void 0 : _d.email) || "Usuario",
                connectionsCount: connections.length,
                connectedAt: (_e = connections[0]) === null || _e === void 0 ? void 0 : _e.connectedAt,
            });
        });
        return {
            isActive: this.wss !== null,
            totalConnections,
            connectedUsers,
            userStats,
        };
    }
    /**
     * Obtener usuarios conectados
     */
    getConnectedUsers() {
        return Array.from(this.userConnections.keys());
    }
    /**
     * Verificar si un usuario está conectado
     */
    isUserConnected(userId) {
        return this.userConnections.has(userId);
    }
    /**
     * Enviar mensaje de chat a usuarios de un canal específico
     */
    sendChatMessageToChannel(channelId, message, excludeUserId) {
        let sentCount = 0;
        this.userConnections.forEach((connections, userId) => {
            var _a;
            // Excluir al usuario que envió el mensaje para evitar duplicados
            if (excludeUserId && userId === excludeUserId) {
                return;
            }
            const user = (_a = connections[0]) === null || _a === void 0 ? void 0 : _a.user;
            if (!user)
                return;
            // TODO: Verificar permisos del usuario para el canal
            // Por ahora enviamos a todos los usuarios conectados
            connections.forEach((connection) => {
                if (this.sendToClient(connection.ws, {
                    type: "chat_message",
                    data: {
                        ...message,
                        channelId,
                    },
                    timestamp: new Date().toISOString(),
                })) {
                    sentCount++;
                }
            });
        });
        console.log(`💬 Mensaje de chat enviado a ${sentCount} conexión(es) en canal ${channelId}`);
        return sentCount;
    }
    /**
     * Enviar actualización de reacción a usuarios del canal
     */
    sendReactionUpdate(channelId, messageId, reactions) {
        let sentCount = 0;
        this.userConnections.forEach((connections) => {
            connections.forEach((connection) => {
                if (this.sendToClient(connection.ws, {
                    type: "chat_reaction_updated",
                    data: {
                        messageId,
                        channelId,
                        reactions,
                    },
                    timestamp: new Date().toISOString(),
                })) {
                    sentCount++;
                }
            });
        });
        return sentCount;
    }
    /**
     * Notificar que un usuario está escribiendo
     */
    sendTypingIndicator(channelId, userId, username, isTyping) {
        let sentCount = 0;
        this.userConnections.forEach((connections, connectedUserId) => {
            // No enviar al usuario que está escribiendo
            if (connectedUserId === userId)
                return;
            connections.forEach((connection) => {
                if (this.sendToClient(connection.ws, {
                    type: "chat_typing",
                    data: {
                        channelId,
                        userId,
                        username,
                        isTyping,
                    },
                    timestamp: new Date().toISOString(),
                })) {
                    sentCount++;
                }
            });
        });
        return sentCount;
    }
    /**
     * Enviar notificación de usuario conectado/desconectado al chat
     */
    sendUserPresenceUpdate(userId, username, isOnline) {
        let sentCount = 0;
        this.userConnections.forEach((connections) => {
            connections.forEach((connection) => {
                if (this.sendToClient(connection.ws, {
                    type: "chat_user_presence",
                    data: {
                        userId,
                        username,
                        isOnline,
                        timestamp: new Date().toISOString(),
                    },
                    timestamp: new Date().toISOString(),
                })) {
                    sentCount++;
                }
            });
        });
        return sentCount;
    }
    /**
     * Cerrar servidor WebSocket
     */
    close() {
        if (this.wss) {
            this.wss.close();
            this.wss = null;
        }
        this.userConnections.clear();
        this.tokenToUserMap.clear();
        console.log("🔌 Servidor WebSocket cerrado");
    }
}
// Exportar instancia singleton
exports.websocketService = new WebSocketService();
exports.default = exports.websocketService;
