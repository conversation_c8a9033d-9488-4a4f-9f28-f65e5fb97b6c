/**
 * WebSocket Service para notificaciones en tiempo real - Con Autenticación
 */

import { WebSocketServer, WebSocket } from "ws";
import { IncomingMessage } from "http";
import jwt from "jsonwebtoken";

interface AuthenticatedWebSocket extends WebSocket {
  userId?: number;
  user?: any;
  isAuthenticated?: boolean;
}

interface UserConnection {
  ws: AuthenticatedWebSocket;
  userId: number;
  user: any;
  connectedAt: Date;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private userConnections: Map<number, UserConnection[]> = new Map();
  private tokenToUserMap: Map<
    string,
    { userId: number; user: any; isAdminToken?: boolean }
  > = new Map();

  /**
   * Inicializar el servidor WebSocket
   */
  initialize(server: any) {
    this.wss = new WebSocketServer({
      server,
      path: "/ws/notifications",
      verifyClient: this.verifyClient.bind(this),
      // Configuración adicional para evitar conflictos con admin panel
      clientTracking: true,
      perMessageDeflate: false,
    });

    this.wss.on("connection", (ws: AuthenticatedWebSocket, req: IncomingMessage) =>
      this.handleConnection(ws, req)
    );

    // Agregar manejo de errores del servidor WebSocket
    this.wss.on("error", (error: Error) => {
      console.error("❌ Error en servidor WebSocket:", error);
    });
  }

  /**
   * Verificar cliente antes de la conexión
   */
  private async verifyClient(info: { req: IncomingMessage }): Promise<boolean> {
    try {
      const url = new URL(info.req.url!, `http://${info.req.headers.host}`);

      // Verificar que la conexión es específicamente para WebSocket notifications
      if (!url.pathname.includes("/ws/notifications")) {
        return false;
      }

      const token =
        url.searchParams.get("token") ||
        info.req.headers.authorization?.replace("Bearer ", "");

      if (!token) {
        return false;
      }

      // Intentar verificar el token como token de usuario normal primero
      let decoded: any = null;
      let isAdminToken = false;

      try {
        // Primero intentar con JWT_SECRET (tokens de usuarios normales)
        decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      } catch (userTokenError) {
        try {
          // Si falla, intentar con ADMIN_JWT_SECRET (tokens de admin)
          decoded = jwt.verify(token, process.env.ADMIN_JWT_SECRET!) as any;
          isAdminToken = true;
        } catch (adminTokenError) {
          return false;
        }
      }

      if (!decoded || (!decoded.id && !decoded.userId)) {
        return false;
      }

      // Obtener el ID del usuario según el tipo de token
      const userId = decoded.id || decoded.userId;

      let user: any = null;

      if (isAdminToken) {
        // Para tokens de admin, buscar en la tabla de admin users
        try {
          user = await strapi.db.query("admin::user").findOne({
            where: { id: userId },
            populate: ["roles"],
          });

          if (user) {
            // Adaptar la estructura para compatibilidad
            user.role = user.roles?.[0] || { name: "admin", type: "admin" };
          }
        } catch (error) {
          return false;
        }
      } else {
        // Para tokens normales, buscar en users-permissions
        try {
          user = await strapi.entityService.findOne(
            "plugin::users-permissions.user",
            userId,
            { populate: ["role"] }
          );
        } catch (error) {
          return false;
        }
      }

      if (!user) {
        return false;
      }

      // Generar un ID único para esta conexión
      const connectionKey = `conn_${userId}_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Guardar datos en la request Y en almacenamiento temporal
      (info.req as any).userId = userId;
      (info.req as any).user = user;
      (info.req as any).connectionKey = connectionKey;
      (info.req as any).isAdminToken = isAdminToken;

      // Almacenar por token para recuperar en handleConnection
      this.tokenToUserMap.set(token, {
        userId: userId,
        user: user,
        isAdminToken: isAdminToken,
      });

      return true;
    } catch (error) {
      console.error("❌ WebSocket: Error verificando cliente:", error);
      return false;
    }
  }

  /**
   * Manejar nueva conexión WebSocket
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: IncomingMessage) {
    // Extraer token de la URL
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const token = url.searchParams.get("token");

    if (!token) {
      console.error("❌ WebSocket: Token no encontrado en la URL");
      (ws as any).close(1008, "Token requerido");
      return;
    }

    // Buscar datos del usuario por token
    const userData = this.tokenToUserMap.get(token);
    if (!userData) {
      console.error("❌ WebSocket: Datos de usuario no encontrados para el token");
      (ws as any).close(1008, "Usuario no autenticado");
      return;
    }

    const { userId, user, isAdminToken } = userData;

    // Limpiar datos temporales
    this.tokenToUserMap.delete(token);

    ws.userId = userId;
    ws.user = user;
    ws.isAuthenticated = true;

    // Agregar conexión al mapa de usuarios
    this.addUserConnection(ws.userId, ws, ws.user);

    // Enviar mensaje de bienvenida
    this.sendToClient(ws, {
      type: "connection",
      message: "Conectado al servidor de notificaciones",
      user: {
        id: ws.user.id || ws.userId,
        username: user.username || user.email || `Usuario ${userId}`,
        isAdmin: isAdminToken || false,
      },
      timestamp: new Date().toISOString(),
    });

    // Manejar mensajes del cliente
    (ws as any).on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleClientMessage(ws, message);
      } catch (error) {
        console.error("❌ Error procesando mensaje del cliente:", error);
      }
    });

    // Manejar desconexión
    (ws as any).on("close", () => {
      this.removeUserConnection(ws.userId, ws);
    });

    // Manejar errores
    (ws as any).on("error", (error) => {
      console.error(`❌ Error WebSocket para usuario ${ws.userId}:`, error);
      this.removeUserConnection(ws.userId, ws);
    });
  }

  /**
   * Manejar mensajes del cliente
   */
  private handleClientMessage(ws: AuthenticatedWebSocket, message: any) {
    switch (message.type) {
      case "ping":
        this.sendToClient(ws, {
          type: "pong",
          timestamp: new Date().toISOString(),
        });
        break;

      case "subscribe":
        // El cliente puede suscribirse a tipos específicos de notificaciones
        break;

      default:
        break;
    }
  }

  /**
   * Agregar conexión de usuario
   */
  private addUserConnection(userId: number, ws: AuthenticatedWebSocket, user: any) {
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, []);
    }

    const connections = this.userConnections.get(userId)!;
    connections.push({
      ws,
      userId,
      user,
      connectedAt: new Date(),
    });

    console.log(
      `📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`
    );
  }

  /**
   * Remover conexión de usuario
   */
  private removeUserConnection(userId: number, ws: AuthenticatedWebSocket) {
    const connections = this.userConnections.get(userId);
    if (!connections) return;

    const index = connections.findIndex((conn) => conn.ws === ws);
    if (index !== -1) {
      connections.splice(index, 1);
    }

    if (connections.length === 0) {
      this.userConnections.delete(userId);
    }

    console.log(
      `📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`
    );
  }

  /**
   * Enviar mensaje a un cliente específico
   */
  private sendToClient(ws: WebSocket, data: any): boolean {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error("❌ Error enviando mensaje:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * Broadcast a todas las conexiones autenticadas
   */
  broadcast(data: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections) => {
      connections.forEach((connection) => {
        if (this.sendToClient(connection.ws, data)) {
          sentCount++;
        }
      });
    });

    console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
    return sentCount;
  }

  /**
   * Enviar notificación a un usuario específico
   */
  sendNotificationToUser(userId: number, notification: any): boolean {
    // Verificar si userId es NaN
    if (isNaN(userId)) {
      console.log(`❌ ERROR: userId es NaN. Valor original: ${userId}`);
      return false;
    }

    const connections = this.userConnections.get(userId);
    if (!connections || connections.length === 0) {
      console.log(`❌ Usuario ${userId} no tiene conexiones activas`);
      return false;
    }

    let sentCount = 0;
    connections.forEach((connection) => {
      if (
        this.sendToClient(connection.ws, {
          type: "notification",
          data: notification,
          timestamp: new Date().toISOString(),
        })
      ) {
        sentCount++;
      }
    });

    console.log(
      `📤 Notificación enviada a ${sentCount} conexión(es) del usuario ${userId}`
    );
    return sentCount > 0;
  }

  /**
   * Enviar notificación a múltiples usuarios
   */
  sendNotificationToUsers(userIds: number[], notification: any): number {
    let totalSent = 0;

    userIds.forEach((userId) => {
      if (this.sendNotificationToUser(userId, notification)) {
        totalSent++;
      }
    });

    return totalSent;
  }

  /**
   * Enviar notificación por rol
   */
  sendNotificationToRole(roleName: string, notification: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections, userId) => {
      const user = connections[0]?.user;

      if (user?.role?.name === roleName) {
        if (this.sendNotificationToUser(userId, notification)) {
          sentCount++;
        }
      }
    });

    console.log(
      `📤 Notificación enviada a ${sentCount} usuario(s) con rol ${roleName}`
    );
    return sentCount;
  }

  /**
   * Obtener estadísticas detalladas
   */
  getStats() {
    const totalConnections = Array.from(this.userConnections.values()).reduce(
      (total, connections) => total + connections.length,
      0
    );

    const connectedUsers = this.userConnections.size;

    const userStats = Array.from(this.userConnections.entries()).map(
      ([userId, connections]) => ({
        userId,
        username:
          connections[0]?.user?.username || connections[0]?.user?.email || "Usuario",
        connectionsCount: connections.length,
        connectedAt: connections[0]?.connectedAt,
      })
    );

    return {
      isActive: this.wss !== null,
      totalConnections,
      connectedUsers,
      userStats,
    };
  }

  /**
   * Obtener usuarios conectados
   */
  getConnectedUsers(): number[] {
    return Array.from(this.userConnections.keys());
  }

  /**
   * Verificar si un usuario está conectado
   */
  isUserConnected(userId: number): boolean {
    return this.userConnections.has(userId);
  }

  /**
   * Enviar mensaje de chat a usuarios de un canal específico
   */
  sendChatMessageToChannel(
    channelId: number,
    message: any,
    excludeUserId?: number
  ): number {
    let sentCount = 0;

    this.userConnections.forEach((connections, userId) => {
      // Excluir al usuario que envió el mensaje para evitar duplicados
      if (excludeUserId && userId === excludeUserId) {
        return;
      }

      const user = connections[0]?.user;
      if (!user) return;

      // TODO: Verificar permisos del usuario para el canal
      // Por ahora enviamos a todos los usuarios conectados
      connections.forEach((connection) => {
        if (
          this.sendToClient(connection.ws, {
            type: "chat_message",
            data: {
              ...message,
              channelId,
            },
            timestamp: new Date().toISOString(),
          })
        ) {
          sentCount++;
        }
      });
    });

    console.log(
      `💬 Mensaje de chat enviado a ${sentCount} conexión(es) en canal ${channelId}`
    );
    return sentCount;
  }

  /**
   * Enviar actualización de reacción a usuarios del canal
   */
  sendReactionUpdate(channelId: number, messageId: number, reactions: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections) => {
      connections.forEach((connection) => {
        if (
          this.sendToClient(connection.ws, {
            type: "chat_reaction_updated",
            data: {
              messageId,
              channelId,
              reactions,
            },
            timestamp: new Date().toISOString(),
          })
        ) {
          sentCount++;
        }
      });
    });

    return sentCount;
  }

  /**
   * Notificar que un usuario está escribiendo
   */
  sendTypingIndicator(
    channelId: number,
    userId: number,
    username: string,
    isTyping: boolean
  ): number {
    let sentCount = 0;

    this.userConnections.forEach((connections, connectedUserId) => {
      // No enviar al usuario que está escribiendo
      if (connectedUserId === userId) return;

      connections.forEach((connection) => {
        if (
          this.sendToClient(connection.ws, {
            type: "chat_typing",
            data: {
              channelId,
              userId,
              username,
              isTyping,
            },
            timestamp: new Date().toISOString(),
          })
        ) {
          sentCount++;
        }
      });
    });

    return sentCount;
  }

  /**
   * Enviar notificación de usuario conectado/desconectado al chat
   */
  sendUserPresenceUpdate(
    userId: number,
    username: string,
    isOnline: boolean
  ): number {
    let sentCount = 0;

    this.userConnections.forEach((connections) => {
      connections.forEach((connection) => {
        if (
          this.sendToClient(connection.ws, {
            type: "chat_user_presence",
            data: {
              userId,
              username,
              isOnline,
              timestamp: new Date().toISOString(),
            },
            timestamp: new Date().toISOString(),
          })
        ) {
          sentCount++;
        }
      });
    });

    return sentCount;
  }

  /**
   * Cerrar servidor WebSocket
   */
  close() {
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    this.userConnections.clear();
    this.tokenToUserMap.clear();
    console.log("🔌 Servidor WebSocket cerrado");
  }
}

// Exportar instancia singleton
export const websocketService = new WebSocketService();
export default websocketService;
