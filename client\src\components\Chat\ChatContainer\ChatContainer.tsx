/**
 * Componente principal del sistema de chat
 */

import React, { useState } from "react";
import { Layout, Typography, Alert, Button, Space, Modal, Input } from "antd";
import {
  WifiOutlined,
  DisconnectOutlined,
  SearchOutlined,
  SettingOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useChat } from "@app/hooks/chat/useChat";
import { readUser } from "@app/services/localStorage.service";
import { ChatChannelList } from "../ChatChannelList/ChatChannelList";
import { ChatMessageList } from "../ChatMessageList/ChatMessageList";
import { ChatInput } from "../ChatInput/ChatInput";
import { ChatHeader } from "../ChatHeader/ChatHeader";
import type { ChatContainerProps } from "@app/types/chat";
import * as S from "./ChatContainer.styles";

const { Sider, Content } = Layout;
const { Title } = Typography;

export const ChatContainer: React.FC<ChatContainerProps> = ({
  className,
  style,
}) => {
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Usuario actual
  const currentUser = readUser();
  const currentUserId = currentUser?.id || 0;

  const {
    channels,
    selectedChannel,
    messages,
    selectedChannelId,
    replyingTo,
    editingMessage,
    error,
    isWebSocketConnected,
    connectionError,
    selectChannel,
    sendMessage,
    editMessage,
    deleteMessage,
    addReaction,
    setReplyingTo,
    setEditingMessage,
    isLoadingChannels,
    isLoadingMessages,
    isSending,
  } = useChat();

  // Manejar envío de mensaje
  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!selectedChannel) return;

    await sendMessage(content, attachments);
  };

  // Manejar búsqueda
  const handleSearch = async () => {
    if (searchQuery.trim()) {
      // TODO: Implementar búsqueda con el nuevo hook
    }
  };

  // Manejar confirmación de eliminación
  const handleDeleteMessage = (messageId: number) => {
    Modal.confirm({
      title: "¿Eliminar mensaje?",
      content: "Esta acción no se puede deshacer.",
      okText: "Eliminar",
      okType: "danger",
      cancelText: "Cancelar",
      onOk: () => deleteMessage(messageId),
    });
  };

  return (
    <S.ChatContainer
      className={className}
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        ...style,
      }}
    >
      {/* Header principal */}
      <S.ChatMainHeader style={{ flexShrink: 0 }}>
        <Title level={3} style={{ margin: 0 }}>
          💬 Chat Comunitario
        </Title>

        <Space>
          {/* Indicador de conexión */}
          <S.ConnectionStatus $isConnected={isWebSocketConnected}>
            {!isWebSocketConnected ? (
              <>
                <DisconnectOutlined />
                <span>Desconectado</span>
              </>
            ) : (
              <>
                <WifiOutlined />
                <span>Conectado</span>
              </>
            )}
          </S.ConnectionStatus>

          {/* Botón de búsqueda */}
          <Button
            type="text"
            icon={<SearchOutlined />}
            onClick={() => setSearchVisible(true)}
            title="Buscar mensajes"
          />

          {/* Botón de configuración */}
          <Button
            type="text"
            icon={<SettingOutlined />}
            title="Configuración del chat"
          />
        </Space>
      </S.ChatMainHeader>

      {/* Error de conexión */}
      {error && (
        <Alert
          message="Error cargando canales"
          description={error.message || "Error al cargar los canales"}
          type="error"
          showIcon
          closable
          action={
            <Button size="small" icon={<ReloadOutlined />}>
              Reintentar
            </Button>
          }
          style={{ margin: "8px 16px" }}
        />
      )}

      {/* Alerta de desconexión WebSocket */}
      {connectionError && (
        <Alert
          message="Problema de conexión"
          description="No se pueden recibir mensajes en tiempo real. Los mensajes se actualizarán al recargar."
          type="warning"
          showIcon
          closable
          style={{ margin: "8px 16px" }}
        />
      )}

      {/* Layout principal */}
      <Layout style={{ flex: 1, minHeight: 0, height: "100%" }}>
        {/* Sidebar con canales */}
        <Sider
          width={280}
          style={{
            background: "transparent",
            borderRight: "1px solid #f0f0f0",
          }}
        >
          <ChatChannelList
            channels={channels}
            selectedChannelId={selectedChannelId}
            onChannelSelect={selectChannel}
            loading={isLoadingChannels}
          />
        </Sider>

        {/* Área principal del chat */}
        <Content
          style={{ display: "flex", flexDirection: "column", minHeight: 0 }}
        >
          {selectedChannel ? (
            <>
              {/* Header del canal */}
              <ChatHeader
                channel={selectedChannel}
                onlineCount={0} // TODO: Implementar conteo de usuarios online
                typingUsers={[]} // TODO: Implementar indicadores de escritura
                isConnected={isWebSocketConnected}
                lastUpdated={new Date()} // TODO: Implementar timestamp real de última actualización
              />

              {/* Lista de mensajes */}
              <ChatMessageList
                messages={messages}
                loading={isLoadingMessages}
                onReply={setReplyingTo}
                onEdit={setEditingMessage}
                onDelete={handleDeleteMessage}
                onReaction={addReaction}
                currentUserId={currentUserId}
              />

              {/* Input para escribir */}
              <ChatInput
                onSendMessage={handleSendMessage}
                replyingTo={replyingTo}
                onCancelReply={() => setReplyingTo(null)}
                disabled={isSending}
                placeholder={`Escribe en ${selectedChannel.name}...`}
                channelId={selectedChannel.id}
              />
            </>
          ) : (
            <S.EmptyState>
              <div>
                <Title level={4} type="secondary">
                  Selecciona un canal para comenzar
                </Title>
                <p>Elige un canal de la lista para ver y enviar mensajes.</p>
              </div>
            </S.EmptyState>
          )}
        </Content>
      </Layout>

      {/* Modal de búsqueda */}
      <Modal
        title="Buscar mensajes"
        open={searchVisible}
        onCancel={() => setSearchVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setSearchVisible(false)}>
            Cancelar
          </Button>,
          <Button key="search" type="primary" onClick={handleSearch}>
            Buscar
          </Button>,
        ]}
      >
        <Input
          placeholder="Buscar en los mensajes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onPressEnter={handleSearch}
          prefix={<SearchOutlined />}
        />

        {/* Resultados de búsqueda */}
        {/* TODO: Implementar resultados de búsqueda */}
      </Modal>
    </S.ChatContainer>
  );
};
